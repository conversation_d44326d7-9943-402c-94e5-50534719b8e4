/**
 * Terminal Chat Interface
 * 
 * Main terminal-based chat interface using blessed.js for rich UI.
 * Handles real-time conversation, model switching, and overlays.
 */

import blessed from 'blessed';
import chalk from 'chalk';
import { EventEmitter } from 'events';
import { AgentLoop } from '../../utils/agent/agent-loop.js';
import { createInputItem } from '../../utils/input-utils.js';
import { logInfo, logError, logDebug, fpsDebugger } from '../../utils/logger/log.js';
import { saveRollout } from '../../utils/storage/save-rollout.js';
import { addToHistory } from '../../utils/storage/command-history.js';
import {
  HistoryOverlay,
  ModelOverlay,
  HelpOverlay,
  ApprovalModeOverlay,
  DiffOverlay,
  SessionsOverlay
} from '../overlays/index.js';
import type {
  AppConfig,
  ResponseItem,
  OverlayModeType,
  ApprovalPolicy,
  TerminalSize,
  SessionData
} from '../../types/index.js';
import { nanoid } from 'nanoid';

export class TerminalChat extends EventEmitter {
  private config: AppConfig;
  private screen: blessed.Widgets.Screen;
  private chatContainer: blessed.Widgets.BoxElement;
  private inputBox: blessed.Widgets.TextareaElement;
  private statusBar: blessed.Widgets.BoxElement;
  private agentLoop: AgentLoop;
  
  // State
  private sessionId: string;
  private items: ResponseItem[] = [];
  private currentModel: string;
  private currentProvider: string;
  private approvalPolicy: ApprovalPolicy;
  private overlayMode: OverlayModeType = 'none';
  private loading = false;
  private terminalSize: TerminalSize;
  
  // UI Elements
  private currentOverlay: blessed.Widgets.Element | null = null;
  private notificationTimeout: NodeJS.Timeout | null = null;

  constructor(config: AppConfig, initialPrompt?: string) {
    super();
    
    this.config = config;
    this.sessionId = nanoid();
    this.currentModel = config.model;
    this.currentProvider = config.provider;
    this.approvalPolicy = config.approvalMode;
    
    this.terminalSize = {
      columns: process.stdout.columns || 80,
      rows: process.stdout.rows || 24,
    };

    // Initialize UI
    this.initializeScreen();
    this.initializeComponents();
    this.setupEventHandlers();
    
    // Initialize agent loop
    this.agentLoop = new AgentLoop({
      model: this.currentModel,
      provider: this.currentProvider,
      approvalPolicy: this.approvalPolicy,
    });

    // Process initial prompt if provided
    if (initialPrompt) {
      this.processInitialPrompt(initialPrompt);
    }

    logInfo(`Terminal chat initialized with session ID: ${this.sessionId}`);
  }

  /**
   * Initialize the blessed screen
   */
  private initializeScreen(): void {
    this.screen = blessed.screen({
      smartCSR: true,
      title: 'Kritrima AI CLI',
      cursor: {
        artificial: true,
        shape: 'line',
        blink: true,
      },
      debug: this.config.debug,
    });

    // Handle screen resize
    this.screen.on('resize', () => {
      this.terminalSize = {
        columns: this.screen.width,
        rows: this.screen.height,
      };
      this.handleResize();
    });
  }

  /**
   * Initialize UI components
   */
  private initializeComponents(): void {
    // Chat container
    this.chatContainer = blessed.box({
      parent: this.screen,
      top: 0,
      left: 0,
      width: '100%',
      height: '100%-4',
      scrollable: true,
      alwaysScroll: true,
      scrollbar: {
        ch: ' ',
        track: {
          bg: 'cyan',
        },
        style: {
          inverse: true,
        },
      },
      border: {
        type: 'line',
      },
      style: {
        fg: 'white',
        border: {
          fg: 'cyan',
        },
      },
      tags: true,
      mouse: true,
    });

    // Input box
    this.inputBox = blessed.textarea({
      parent: this.screen,
      bottom: 1,
      left: 0,
      width: '100%',
      height: 3,
      border: {
        type: 'line',
      },
      style: {
        fg: 'white',
        border: {
          fg: 'cyan',
        },
      },
      inputOnFocus: true,
      scrollable: true,
      keys: true,
      mouse: true,
      tags: false,
    });

    // Status bar
    this.statusBar = blessed.box({
      parent: this.screen,
      bottom: 0,
      left: 0,
      width: '100%',
      height: 1,
      style: {
        fg: 'white',
        bg: 'blue',
      },
      tags: true,
    });

    this.updateStatusBar();
  }

  /**
   * Setup event handlers
   */
  private setupEventHandlers(): void {
    // Global key handlers
    this.screen.key(['escape', 'C-c'], () => {
      if (this.overlayMode !== 'none') {
        this.closeOverlay();
      } else {
        this.shutdown();
      }
    });

    this.screen.key(['C-l'], () => {
      this.clearConversation();
    });

    this.screen.key(['C-h'], () => {
      this.showOverlay('help');
    });

    this.screen.key(['C-m'], () => {
      this.showOverlay('model');
    });

    this.screen.key(['C-r'], () => {
      this.showOverlay('history');
    });

    // Input box handlers
    this.inputBox.key(['enter'], () => {
      this.handleInput();
    });

    this.inputBox.key(['C-enter'], () => {
      this.inputBox.insertText('\n');
    });

    this.inputBox.key(['tab'], () => {
      this.handleTabCompletion();
    });

    // Chat container scroll
    this.chatContainer.key(['j', 'down'], () => {
      this.chatContainer.scroll(1);
      this.screen.render();
    });

    this.chatContainer.key(['k', 'up'], () => {
      this.chatContainer.scroll(-1);
      this.screen.render();
    });

    // Focus management
    this.inputBox.focus();
  }

  /**
   * Start the terminal chat interface
   */
  public async start(): Promise<void> {
    try {
      // Display welcome message
      this.addSystemMessage('Welcome to Kritrima AI CLI! Type your message or use slash commands.');
      this.addSystemMessage('Available commands: /help, /model, /history, /clear, /approval');
      
      // Render initial screen
      this.screen.render();
      
      // Start FPS debugging if enabled
      if (this.config.debug) {
        this.startFPSDebugging();
      }

      logInfo('Terminal chat interface started');
      
    } catch (error) {
      logError('Failed to start terminal chat', error);
      throw error;
    }
  }

  /**
   * Handle user input
   */
  private async handleInput(): Promise<void> {
    const input = this.inputBox.getValue().trim();
    
    if (!input) {
      return;
    }

    // Clear input
    this.inputBox.clearValue();
    this.screen.render();

    // Add to history
    addToHistory(input, this.sessionId);

    // Handle slash commands
    if (input.startsWith('/')) {
      this.handleSlashCommand(input);
      return;
    }

    // Process as AI input
    await this.processUserInput(input);
  }

  /**
   * Handle slash commands
   */
  private handleSlashCommand(command: string): void {
    const [cmd, ...args] = command.split(' ');
    
    switch (cmd.toLowerCase()) {
      case '/help':
        this.showOverlay('help');
        break;
      case '/model':
        this.showOverlay('model');
        break;
      case '/history':
        this.showOverlay('history');
        break;
      case '/sessions':
        this.showOverlay('sessions');
        break;
      case '/approval':
        this.showOverlay('approval');
        break;
      case '/diff':
        this.showOverlay('diff');
        break;
      case '/clear':
        this.clearConversation();
        break;
      case '/compact':
        this.compactConversation();
        break;
      case '/bug':
        this.generateBugReport();
        break;
      default:
        this.addSystemMessage(`Unknown command: ${cmd}. Type /help for available commands.`);
    }
  }

  /**
   * Process user input through AI
   */
  private async processUserInput(input: string): Promise<void> {
    try {
      this.setLoading(true);
      
      // Add user message to chat
      const userMessage: ResponseItem = {
        id: nanoid(),
        type: 'message',
        timestamp: Date.now(),
        content: [{ type: 'input_text', text: input }],
      };
      
      this.addMessage(userMessage);

      // Create input item for agent
      const inputItem = await createInputItem(input);
      
      // Process through agent loop
      const response = await this.agentLoop.processInput(inputItem);
      
      // Add response to chat
      this.addMessage(response);
      
      // Save session
      this.saveSession();
      
    } catch (error) {
      logError('Failed to process user input', error);
      this.addSystemMessage(`Error: ${error.message}`);
    } finally {
      this.setLoading(false);
    }
  }

  /**
   * Add message to chat
   */
  private addMessage(message: ResponseItem): void {
    this.items.push(message);
    this.renderMessage(message);
    this.scrollToBottom();
    this.screen.render();
  }

  /**
   * Add system message
   */
  private addSystemMessage(text: string): void {
    const message: ResponseItem = {
      id: nanoid(),
      type: 'system',
      timestamp: Date.now(),
      content: [{ type: 'input_text', text }],
    };
    
    this.addMessage(message);
  }

  /**
   * Render a message in the chat container
   */
  private renderMessage(message: ResponseItem): void {
    const timestamp = new Date(message.timestamp).toLocaleTimeString();
    let content = '';
    
    switch (message.type) {
      case 'message':
        if (message.content[0]?.type === 'input_text') {
          content = `{cyan-fg}[${timestamp}] User:{/cyan-fg} ${message.content[0].text}`;
        }
        break;
      case 'system':
        if (message.content[0]?.type === 'input_text') {
          content = `{yellow-fg}[${timestamp}] System:{/yellow-fg} ${message.content[0].text}`;
        }
        break;
      case 'error':
        if (message.content[0]?.type === 'input_text') {
          content = `{red-fg}[${timestamp}] Error:{/red-fg} ${message.content[0].text}`;
        }
        break;
    }
    
    if (content) {
      this.chatContainer.insertLine(this.chatContainer.getLines().length, content);
    }
  }

  /**
   * Update status bar
   */
  private updateStatusBar(): void {
    const status = [
      `Model: ${this.currentModel}`,
      `Provider: ${this.currentProvider}`,
      `Approval: ${this.approvalPolicy}`,
      `Messages: ${this.items.length}`,
    ].join(' | ');
    
    this.statusBar.setContent(status);
  }

  /**
   * Set loading state
   */
  private setLoading(loading: boolean): void {
    this.loading = loading;
    this.updateStatusBar();
    
    if (loading) {
      this.statusBar.setContent(this.statusBar.getContent() + ' | {yellow-fg}Thinking...{/yellow-fg}');
    }
    
    this.screen.render();
  }

  /**
   * Scroll to bottom of chat
   */
  private scrollToBottom(): void {
    this.chatContainer.setScrollPerc(100);
  }

  /**
   * Handle screen resize
   */
  private handleResize(): void {
    logDebug(`Screen resized to ${this.terminalSize.columns}x${this.terminalSize.rows}`);
    this.screen.render();
  }

  /**
   * Start FPS debugging
   */
  private startFPSDebugging(): void {
    setInterval(() => {
      fpsDebugger.frame();
    }, 16); // ~60 FPS
  }

  /**
   * Process initial prompt
   */
  private async processInitialPrompt(prompt: string): Promise<void> {
    // Delay to allow UI to initialize
    setTimeout(async () => {
      await this.processUserInput(prompt);
    }, 100);
  }

  /**
   * Show overlay based on mode
   */
  private showOverlay(mode: OverlayModeType): void {
    // Close any existing overlay
    this.closeOverlay();

    this.overlayMode = mode;

    switch (mode) {
      case 'history':
        this.showHistoryOverlay();
        break;
      case 'model':
        this.showModelOverlay();
        break;
      case 'help':
        this.showHelpOverlay();
        break;
      case 'approval':
        this.showApprovalOverlay();
        break;
      case 'diff':
        this.showDiffOverlay();
        break;
      case 'sessions':
        this.showSessionsOverlay();
        break;
      default:
        logDebug(`Unknown overlay mode: ${mode}`);
    }
  }

  private closeOverlay(): void {
    this.overlayMode = 'none';
    if (this.currentOverlay) {
      this.currentOverlay.destroy();
      this.currentOverlay = null;
    }
    this.screen.render();
  }

  private clearConversation(): void {
    this.items = [];
    this.chatContainer.setContent('');
    this.addSystemMessage('Conversation cleared.');
  }

  private compactConversation(): void {
    this.addSystemMessage('Conversation compacted (feature coming soon).');
  }

  private generateBugReport(): void {
    this.addSystemMessage('Bug report generation (feature coming soon).');
  }

  private handleTabCompletion(): void {
    // Tab completion implementation
    logDebug('Tab completion triggered');
  }

  private saveSession(): void {
    try {
      saveRollout(this.sessionId, this.items);
    } catch (error) {
      logError('Failed to save session', error);
    }
  }

  /**
   * Show history overlay
   */
  private showHistoryOverlay(): void {
    this.currentOverlay = new HistoryOverlay({
      parent: this.screen,
      onSelect: (command: string) => {
        this.inputBox.setValue(command);
        this.closeOverlay();
      },
      onClose: () => {
        this.closeOverlay();
      },
    });
    this.currentOverlay.show();
  }

  /**
   * Show model overlay
   */
  private showModelOverlay(): void {
    this.currentOverlay = new ModelOverlay({
      parent: this.screen,
      currentProvider: this.currentProvider,
      currentModel: this.currentModel,
      onSelect: (provider: string, model: string) => {
        this.currentProvider = provider;
        this.currentModel = model;
        this.updateStatusBar();
        this.closeOverlay();
      },
      onClose: () => {
        this.closeOverlay();
      },
    });
    this.currentOverlay.show();
  }

  /**
   * Show help overlay
   */
  private showHelpOverlay(): void {
    this.currentOverlay = new HelpOverlay({
      parent: this.screen,
      onClose: () => {
        this.closeOverlay();
      },
    });
    this.currentOverlay.show();
  }

  /**
   * Show approval overlay
   */
  private showApprovalOverlay(): void {
    this.currentOverlay = new ApprovalModeOverlay({
      parent: this.screen,
      currentMode: this.approvalPolicy,
      onSelect: (mode: ApprovalPolicy) => {
        this.approvalPolicy = mode;
        this.updateStatusBar();
        this.closeOverlay();
      },
      onClose: () => {
        this.closeOverlay();
      },
    });
    this.currentOverlay.show();
  }

  /**
   * Show diff overlay
   */
  private showDiffOverlay(): void {
    this.currentOverlay = new DiffOverlay({
      parent: this.screen,
      workingDirectory: this.config.workingDirectory,
      onClose: () => {
        this.closeOverlay();
      },
    });
    this.currentOverlay.show();
  }

  /**
   * Show sessions overlay
   */
  private showSessionsOverlay(): void {
    this.currentOverlay = new SessionsOverlay({
      parent: this.screen,
      onLoad: (sessionData: SessionData) => {
        this.loadSession(sessionData);
        this.closeOverlay();
      },
      onClose: () => {
        this.closeOverlay();
      },
    });
    this.currentOverlay.show();
  }

  /**
   * Load session data
   */
  private loadSession(sessionData: SessionData): void {
    try {
      this.items = sessionData.items;
      this.sessionId = sessionData.id;

      // Update configuration
      if (sessionData.config.model) {
        this.currentModel = sessionData.config.model;
      }
      if (sessionData.config.provider) {
        this.currentProvider = sessionData.config.provider;
      }
      if (sessionData.config.approvalMode) {
        this.approvalPolicy = sessionData.config.approvalMode;
      }

      // Re-render chat
      this.chatContainer.setContent('');
      for (const item of this.items) {
        this.renderMessage(item);
      }

      this.updateStatusBar();
      this.scrollToBottom();
      this.screen.render();

      this.addSystemMessage(`Session loaded: ${sessionData.metadata.messageCount} messages`);

      logInfo(`Loaded session ${sessionData.id}`);
    } catch (error) {
      logError('Failed to load session', error);
      this.addSystemMessage(`Error loading session: ${error.message}`);
    }
  }

  /**
   * Shutdown the terminal chat
   */
  public shutdown(): void {
    try {
      this.saveSession();
      this.screen.destroy();
      process.exit(0);
    } catch (error) {
      logError('Error during shutdown', error);
      process.exit(1);
    }
  }
}
